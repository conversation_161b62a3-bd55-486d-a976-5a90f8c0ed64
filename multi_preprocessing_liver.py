import numpy as np
from glob import glob
from tqdm import tqdm
import h5py
import SimpleIT<PERSON> as sitk
from skimage import transform, measure
import os
import matplotlib.pyplot as plt


def ImageResample(sitk_image, new_spacing = [0.8027340173721313, 0.8027340173721313, 2.5], is_label = False):
    '''
    sitk_image:
    new_spacing: x,y,z
    is_label: if True, using Interpolator `sitk.sitkNearestNeighbor`
    '''
    size = np.array(sitk_image.GetSize())
    spacing = np.array(sitk_image.GetSpacing())
    new_spacing = np.array(new_spacing)
    new_size = size * spacing / new_spacing
    new_spacing_refine = size * spacing / new_size
    # new_spacing_refine = [float(s) for s in new_spacing_refine]
    new_size = [int(s) for s in new_size]
    if not is_label:
        print(f'Original size: {size}')
        print(f'New size: {new_size}')
    #     print(f'Original spacing: {spacing}')
    #     print(f'New spacing: {new_spacing_refine}')
    resample = sitk.ResampleImageFilter()
    resample.SetOutputDirection(sitk_image.GetDirection())
    resample.SetOutputOrigin(sitk_image.GetOrigin())
    resample.SetSize(new_size)
    resample.SetOutputSpacing(new_spacing_refine)

    if is_label:
        resample.SetInterpolator(sitk.sitkNearestNeighbor)
    else:
        resample.SetInterpolator(sitk.sitkBSpline)
    newimage = resample.Execute(sitk_image)
    return newimage



def set_window_wl_ww(tensor, sl_window=None):
    assert(sl_window != None)
    w_min, w_max = sl_window
    tensor[tensor < w_min] = w_min
    tensor[tensor > w_max] = w_max
    tensor = (tensor - w_min) / (w_max - w_min)
    ### min max Normalization
    return tensor

# 根据标签图像（label）中的非零区域来裁剪图像（image）和标签本身
def crop_roi(image, label):
    assert(image.shape == label.shape)
    # print(f'Before crop: {image.shape}')
    ### crop based on lung segmentation
    w, h, d = label.shape

    tempL = np.nonzero(label)
    minx, maxx = np.min(tempL[0]), np.max(tempL[0])
    miny, maxy = np.min(tempL[1]), np.max(tempL[1])
    minz, maxz = np.min(tempL[2]), np.max(tempL[2])
    # print('minz, maxz: ', minz, maxz)
    # input()

    # px py pz 是需要扩展的边界的长度
    px = max(output_size[0] - (maxx - minx), 0) // 2    # DCB: 扩展边界，防止连预设的96 96 96 都不够？
    py = max(output_size[1] - (maxy - miny), 0) // 2
    pz = max(output_size[2] - (maxz - minz), 0) // 2
    minx = max(minx - px - 25, 0) #np.random.randint(5, 10)
    maxx = min(maxx + px + 25, w) #np.random.randint(5, 10)
    miny = max(miny - py - 25, 0)
    maxy = min(maxy + py + 25, h)
    minz = max(minz - pz - 25, 0)
    maxz = min(maxz + pz + 25, d)
    
    image = image[minx:maxx, miny:maxy, minz:maxz].astype(np.float32)   # 不知道为什么，原始的代码只在xy这两个轴上做crop，不管z轴
    label = label[minx:maxx, miny:maxy, minz:maxz].astype(np.float32)   # 这里进行了修改使得在z轴上也进行裁剪
    return image, label


import multiprocessing
from functools import partial

def process_one_case(name_image, base_path, dataset_name, HU_window_dic):
    name_label = name_image.replace('imagesTr', 'labelsTr')
    # if name_image == '/home/<USER>/data/tumor/Dataset017_Liver/imagesTr/liver_85.nii.gz':
    #     return

    try:
        # print('Processing image: ', name_image)
        itk_img = sitk.ReadImage(name_image)
        itk_img = ImageResample(itk_img)
        image = sitk.GetArrayFromImage(itk_img)
        image = np.transpose(image, (2,1,0))

        itk_label = sitk.ReadImage(name_label)
        itk_label = ImageResample(itk_label, is_label=True)
        label = sitk.GetArrayFromImage(itk_label)
        label = np.transpose(label, (2,1,0))

        assert image.shape == label.shape
        image = set_window_wl_ww(image, HU_window_dic[dataset_name])
        # image, label = crop_roi(image, label)
        image = (image - np.mean(image)) / np.std(image)
        # print(image.shape)
        h5_dir = 'h5_nnunet'

        os.makedirs(f'{base_path}/{h5_dir}/', exist_ok=True)
        base_name = os.path.basename(name_image)
        with h5py.File(f'{base_path}/{h5_dir}/'+base_name[:-7] + '.h5', 'w') as f:
            f.create_dataset('image', data=image, compression="gzip")
            f.create_dataset('label', data=label, compression="gzip")
    except Exception as e:
        print(f'Error processing {name_image}: {e}')


if __name__ == '__main__':
    base_path = '/home/<USER>/data/tumor/Dataset016_Pancreas'
    dataset_name = 'Pancreas'
    list_label = glob(f'{base_path}/imagesTr/*')

    HU_window_dic = {
        'Lung': [-1000, 1000],
        'HepaticVessel': [0, 230],
        'Spleen': [-125, 275],
        'Colon': [-57, 175],
        'Pancreas': [-92, 217],  # nnunet: [-92, 217]  arxiv:[-87, 199]
        'Liver': [-15, 198],
        'KiTS2023': [-79, 304],
    }

    output_size = [40, 224, 224]  # 确保 crop_roi 可用
    num_workers = min(8, multiprocessing.cpu_count())  # 可调整并行线程数
    with multiprocessing.Pool(num_workers) as pool:
        # tqdm with imap_unordered for progress
        process_func = partial(process_one_case, base_path=base_path, dataset_name=dataset_name, HU_window_dic=HU_window_dic)
        for _ in tqdm(pool.imap_unordered(process_func, list_label), total=len(list_label)):
            pass