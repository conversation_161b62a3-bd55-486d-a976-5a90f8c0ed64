#!/usr/bin/env python3
"""
Liver数据集2D预处理脚本 - 处理所有切片版本 (使用SimpleITK)
将3D NIfTI文件转换为2D H5切片，应用HU窗口、尺寸调整和spacing_z处理
保留所有切片，不进行ROI过滤
"""

import SimpleITK as sitk
import numpy as np
import h5py
import os
from pathlib import Path
import cv2
from tqdm import tqdm
import argparse
from scipy import ndimage
from multiprocessing import Pool, Manager
import time

def apply_hu_window(image, window_min=-21, window_max=189):
    """
    应用HU窗口并归一化到[0,1]
    
    Args:
        image (np.ndarray): 输入图像
        window_min (int): HU窗口最小值
        window_max (int): HU窗口最大值
    
    Returns:
        np.ndarray: 处理后的图像
    """
    # 应用HU窗口
    image_windowed = np.clip(image, window_min, window_max)
    
    # 归一化到[0,1]
    image_normalized = (image_windowed - window_min) / (window_max - window_min)
    
    return image_normalized.astype(np.float32)

def resize_image(image, target_size=(256, 256)):
    """
    调整图像尺寸到目标大小
    
    Args:
        image (np.ndarray): 输入图像
        target_size (tuple): 目标尺寸 (height, width)
    
    Returns:
        np.ndarray: 调整后的图像
    """
    if image.shape[:2] == target_size:
        return image
    
    # 使用双线性插值调整尺寸
    resized = cv2.resize(image, (target_size[1], target_size[0]), interpolation=cv2.INTER_LINEAR)
    return resized

def resize_label(label, target_size=(256, 256)):
    """
    调整标签尺寸到目标大小（使用最近邻插值）

    Args:
        label (np.ndarray): 输入标签
        target_size (tuple): 目标尺寸 (height, width)

    Returns:
        np.ndarray: 调整后的标签
    """
    if label.shape[:2] == target_size:
        return label

    # 使用最近邻插值调整尺寸，保持标签值不变
    resized = cv2.resize(label, (target_size[1], target_size[0]), interpolation=cv2.INTER_NEAREST)
    return resized

def calculate_roi_ratio(label_slice):
    """
    计算切片中ROI区域的比例

    Args:
        label_slice (np.ndarray): 标签切片

    Returns:
        float: ROI区域比例 (0-1之间)
    """
    total_pixels = label_slice.size
    roi_pixels = np.sum(label_slice > 0)  # 非背景像素
    return roi_pixels / total_pixels if total_pixels > 0 else 0.0

def resample_volume_z(volume, original_spacing_z, target_spacing_z=3.0):
    """
    在Z轴方向重采样体积数据到目标spacing
    
    Args:
        volume (np.ndarray): 输入体积数据 (H, W, D)
        original_spacing_z (float): 原始Z轴spacing
        target_spacing_z (float): 目标Z轴spacing
    
    Returns:
        np.ndarray: 重采样后的体积数据
    """
    if abs(original_spacing_z - target_spacing_z) < 1e-6:
        return volume
    
    # 计算缩放因子
    scale_factor = original_spacing_z / target_spacing_z
    
    # 计算新的Z轴尺寸
    original_depth = volume.shape[2]
    new_depth = int(round(original_depth * scale_factor))
    
    print(f"   Z轴重采样: {original_depth} -> {new_depth} (spacing: {original_spacing_z:.3f} -> {target_spacing_z:.3f})")
    
    # 使用scipy进行3D插值重采样
    zoom_factors = (1.0, 1.0, scale_factor)  # 只在Z轴方向缩放
    
    # 对图像使用线性插值
    if volume.dtype in [np.float32, np.float64]:
        resampled = ndimage.zoom(volume, zoom_factors, order=1, mode='nearest')
    else:
        # 对标签使用最近邻插值
        resampled = ndimage.zoom(volume, zoom_factors, order=0, mode='nearest')
    
    return resampled

def process_single_case_worker(args):
    """
    多进程工作函数包装器
    """
    return process_single_case(*args)

def process_single_case(image_path, label_path, output_dir, case_name,
                       hu_window=(-21, 189), target_size=(256, 256),
                       target_spacing_z=3.0, roi_filter_enabled=False, roi_threshold=0.05):
    """
    处理单个病例

    Args:
        image_path (str): 图像文件路径
        label_path (str): 标签文件路径
        output_dir (str): 输出目录
        case_name (str): 病例名称（如liver_1）
        hu_window (tuple): HU窗口 (min, max)
        target_size (tuple): 目标尺寸
        target_spacing_z (float): 目标Z轴spacing
        roi_filter_enabled (bool): 是否启用ROI过滤
        roi_threshold (float): ROI区域比例阈值 (0-1之间)

    Returns:
        dict: 处理结果统计
    """
    try:
        # 使用SimpleITK加载数据
        print(f"🔍 处理病例: {case_name}")
        img_sitk = sitk.ReadImage(image_path)
        label_sitk = sitk.ReadImage(label_path)
        
        # 转换为numpy数组
        img_data = sitk.GetArrayFromImage(img_sitk)
        label_data = sitk.GetArrayFromImage(label_sitk)
        
        # SimpleITK的数组是(Z, Y, X)格式，需要转置为(X, Y, Z)
        img_data = np.transpose(img_data, (2, 1, 0))
        label_data = np.transpose(label_data, (2, 1, 0))
        
        # 检查形状匹配
        if img_data.shape != label_data.shape:
            print(f"⚠️  警告: {case_name} 图像和标签形状不匹配!")
            return {"error": "Shape mismatch"}
        
        # 获取原始spacing (SimpleITK的spacing是(X, Y, Z)格式)
        original_spacing = img_sitk.GetSpacing()
        original_spacing_z = original_spacing[2]
        
        print(f"📊 原始形状: {img_data.shape}")
        print(f"📏 原始spacing: {original_spacing}")
        
        # Z轴重采样
        if abs(original_spacing_z - target_spacing_z) > 1e-6:
            print(f"🔄 进行Z轴重采样...")
            img_data = resample_volume_z(img_data, original_spacing_z, target_spacing_z)
            label_data = resample_volume_z(label_data, original_spacing_z, target_spacing_z)
            print(f"📊 重采样后形状: {img_data.shape}")
        else:
            print(f"✅ Z轴spacing已符合要求，跳过重采样")
        
        total_slices = img_data.shape[2]
        saved_slices = []
        filtered_slices = 0

        # 处理每个切片
        filter_desc = f"处理 {case_name} 切片 (ROI过滤: {'启用' if roi_filter_enabled else '禁用'})"
        for z in tqdm(range(total_slices), desc=filter_desc):
            # 提取切片
            img_slice = img_data[:, :, z]
            label_slice = label_data[:, :, z]

            # ROI过滤检查
            if roi_filter_enabled:
                roi_ratio = calculate_roi_ratio(label_slice)
                if roi_ratio < roi_threshold:
                    filtered_slices += 1
                    continue  # 跳过ROI比例低于阈值的切片

            # 应用HU窗口
            img_processed = apply_hu_window(img_slice, hu_window[0], hu_window[1])

            # 调整尺寸
            img_resized = resize_image(img_processed, target_size)
            label_resized = resize_label(label_slice, target_size)

            # 确保标签是整数类型
            label_resized = label_resized.astype(np.uint8)
            
            # 生成输出文件名 - 使用连续编号（从001开始）
            slice_number = len(saved_slices) + 1  # 连续编号
            slice_filename = f"{case_name}_{slice_number:03d}.h5"
            output_path = os.path.join(output_dir, slice_filename)

            # 保存为H5文件
            with h5py.File(output_path, 'w') as f:
                f.create_dataset('image', data=img_resized, compression='gzip', compression_opts=9)
                f.create_dataset('label', data=label_resized, compression='gzip', compression_opts=9)

                # 保存元数据
                f.attrs['case_name'] = case_name
                f.attrs['original_slice_index'] = z  # 原始切片索引
                f.attrs['slice_number'] = slice_number  # 连续切片编号（从1开始）
                f.attrs['original_shape'] = img_sitk.GetSize()[::-1]  # SimpleITK size是(X,Y,Z)，转为(Z,Y,X)
                f.attrs['resampled_shape'] = img_data.shape
                f.attrs['target_size'] = target_size
                f.attrs['hu_window'] = hu_window
                f.attrs['original_spacing'] = original_spacing
                f.attrs['target_spacing_z'] = target_spacing_z
                f.attrs['actual_spacing_z'] = target_spacing_z
                f.attrs['roi_filter_enabled'] = roi_filter_enabled
                f.attrs['roi_threshold'] = roi_threshold
                if roi_filter_enabled:
                    f.attrs['roi_ratio'] = calculate_roi_ratio(label_resized)

            saved_slices.append({
                'original_slice_index': z,
                'slice_number': slice_number,
                'filename': slice_filename,
                'output_path': output_path
            })
        
        saved_count = len(saved_slices)
        result = {
            'case_name': case_name,
            'total_slices': total_slices,
            'saved_slices': saved_slices,
            'saved_count': saved_count,
            'filtered_count': filtered_slices,
            'original_shape': img_sitk.GetSize()[::-1],  # 转为(Z,Y,X)格式
            'resampled_shape': img_data.shape,
            'original_spacing': original_spacing,
            'target_spacing_z': target_spacing_z,
            'roi_filter_enabled': roi_filter_enabled,
            'roi_threshold': roi_threshold
        }

        if roi_filter_enabled:
            print(f"✅ {case_name} 处理完成: {total_slices} 个切片，保存 {saved_count} 个，过滤 {filtered_slices} 个 (ROI阈值: {roi_threshold})")
        else:
            print(f"✅ {case_name} 处理完成: {total_slices} 个切片全部保存")
        return result
        
    except Exception as e:
        print(f"❌ 处理 {case_name} 时出错: {str(e)}")
        return {"error": str(e), "case_name": case_name}

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Liver数据集2D预处理 - 保留所有切片 (使用SimpleITK)')
    parser.add_argument('--image_dir', default='/home/<USER>/data/liver_tumor/imagesTs_MSWAL',
                       help='图像目录路径')
    parser.add_argument('--label_dir', default='/home/<USER>/data/liver_tumor/inferTs_MSWAL',
                       help='标签目录路径')
    parser.add_argument('--output_dir', default='/home/<USER>/data/liver_tumor/h5',
                       help='输出目录路径')
    parser.add_argument('--list_file', default='/home/<USER>/data/liver_tumor/2d_all.list',
                       help='输出列表文件路径')
    parser.add_argument('--hu_min', type=int, default=-21, help='HU窗口最小值')
    parser.add_argument('--hu_max', type=int, default=189, help='HU窗口最大值')
    parser.add_argument('--size', type=int, default=256, help='目标图像尺寸')
    parser.add_argument('--spacing_z', type=float, default=3.0, help='目标Z轴spacing')
    parser.add_argument('--start_case', type=int, default=0, help='开始处理的病例编号')
    parser.add_argument('--end_case', type=int, default=None, help='结束处理的病例编号')
    parser.add_argument('--num_processes', type=int, default=8, help='并行处理进程数')
    parser.add_argument('--roi_filter', type=int, default=1, help='启用ROI过滤')
    parser.add_argument('--roi_threshold', type=float, default=0.05, help='ROI区域比例阈值 (0-1之间，默认0.05)')

    args = parser.parse_args()

    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # 创建列表文件目录
    list_dir = Path(args.list_file).parent
    list_dir.mkdir(parents=True, exist_ok=True)

    filter_mode = "ROI过滤模式" if args.roi_filter else "保留所有切片"
    print(f"🏥 Liver数据集2D预处理 - {filter_mode} (使用SimpleITK)")
    print("=" * 60)
    print(f"📁 图像目录: {args.image_dir}")
    print(f"📁 标签目录: {args.label_dir}")
    print(f"📁 输出目录: {args.output_dir}")
    print(f"📁 列表文件: {args.list_file}")
    print(f"🪟 HU窗口: [{args.hu_min}, {args.hu_max}]")
    print(f"📏 目标尺寸: {args.size}×{args.size}")
    print(f"📏 目标Z轴spacing: {args.spacing_z} mm")
    print(f"🔄 并行进程数: {args.num_processes}")
    print(f"🎯 ROI过滤: {'启用' if args.roi_filter else '禁用'}")
    if args.roi_filter:
        print(f"📊 ROI阈值: {args.roi_threshold} ({args.roi_threshold*100:.1f}%)")
    print("=" * 60)

    # 获取所有病例文件
    image_files = sorted([f for f in os.listdir(args.image_dir) if f.endswith('.nii.gz')])

    if args.end_case is not None:
        image_files = image_files[args.start_case:args.end_case]
    else:
        image_files = image_files[args.start_case:]

    print(f"📋 找到 {len(image_files)} 个病例需要处理")

    # 准备处理任务
    tasks = []
    valid_cases = []

    for image_file in image_files:
        case_name = image_file.replace('.nii.gz', '')
        image_path = os.path.join(args.image_dir, image_file)
        label_filename = image_file.rsplit('_', 1)[0] + '.nii.gz'
        label_path = os.path.join(args.label_dir, label_filename)
        # label_path = os.path.join(args.label_dir, image_file)

        # 检查标签文件是否存在
        if not os.path.exists(label_path):
            print(f"⚠️  跳过 {case_name}: 标签文件不存在")
            continue

        # 准备任务参数
        task_args = (
            image_path, label_path, args.output_dir, case_name,
            (args.hu_min, args.hu_max),
            (args.size, args.size),
            args.spacing_z,
            args.roi_filter,
            args.roi_threshold
        )
        tasks.append(task_args)
        valid_cases.append(case_name)

    print(f"📋 准备处理 {len(tasks)} 个有效病例")

    # 多进程处理
    print(f"🚀 启动 {args.num_processes} 个进程进行并行处理...")
    start_time = time.time()

    with Pool(processes=args.num_processes) as pool:
        # 使用imap_unordered获取进度
        results = []
        error_cases = []
        completed_cases = 0

        with tqdm(total=len(tasks), desc="🔄 处理病例", unit="病例",
                 bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]") as pbar:
            for result in pool.imap_unordered(process_single_case_worker, tasks):
                results.append(result)
                completed_cases += 1

                if "error" not in result:
                    postfix_info = {
                        "当前": result['case_name'],
                        "总切片": result['total_slices'],
                        "保存": result['saved_count'],
                        "成功": completed_cases - len(error_cases),
                        "失败": len(error_cases)
                    }
                    if result.get('roi_filter_enabled', False):
                        postfix_info["过滤"] = result['filtered_count']
                    pbar.set_postfix(postfix_info)
                else:
                    error_cases.append(result.get('case_name', 'Unknown'))
                    pbar.set_postfix({
                        "失败": result.get('case_name', 'Unknown'),
                        "成功": completed_cases - len(error_cases),
                        "失败数": len(error_cases)
                    })

                pbar.update(1)

    end_time = time.time()
    processing_time = end_time - start_time

    # 处理结果统计
    all_results = []
    total_processed = 0
    total_saved = 0
    total_filtered = 0
    all_file_paths = []
    failed_cases = []

    for result in results:
        if "error" not in result:
            all_results.append(result)
            total_processed += result['total_slices']
            total_saved += result['saved_count']
            total_filtered += result.get('filtered_count', 0)

            # 收集所有文件路径
            for slice_info in result['saved_slices']:
                all_file_paths.append(slice_info['output_path'])
        else:
            failed_cases.append({
                'case_name': result.get('case_name', 'Unknown'),
                'error': result['error']
            })

    print(f"\n⏱️  总处理时间: {processing_time:.2f} 秒")
    if len(all_results) > 0:
        print(f"⚡ 平均每个病例: {processing_time/len(all_results):.2f} 秒")

    # 按照要求排序：病人号优先级高于切片号
    print(f"\n🔄 排序文件列表...")
    all_file_paths.sort(key=lambda x: (
        int(os.path.basename(x).split('_')[1]),  # 病人号
        int(os.path.basename(x).split('_')[2].split('.')[0])  # 切片号
    ))

    # 写入列表文件
    print(f"📝 写入列表文件: {args.list_file}")
    with open(args.list_file, 'w') as f:
        for file_path in all_file_paths:
            f.write(file_path + '\n')

    # 输出最终统计
    print(f"\n🎉 处理完成!")
    print("=" * 60)
    print(f"📊 处理统计:")
    print(f"   总病例数: {len(tasks)}")
    print(f"   成功处理病例数: {len(all_results)}")
    print(f"   失败病例数: {len(failed_cases)}")
    print(f"   成功率: {len(all_results)/len(tasks)*100:.1f}%")
    print(f"   总切片数: {total_processed}")
    print(f"   保存切片数: {total_saved}")
    if args.roi_filter:
        print(f"   过滤切片数: {total_filtered}")
        save_ratio = (total_saved / total_processed * 100) if total_processed > 0 else 0
        print(f"   保存比例: {save_ratio:.1f}% (ROI阈值: {args.roi_threshold})")
    else:
        print(f"   保存比例: 100.0% (保留所有切片)")
    print(f"   列表文件: {args.list_file}")
    print(f"   列表文件包含: {len(all_file_paths)} 个文件路径")

    # 输出错误报告
    if failed_cases:
        print(f"\n❌ 处理失败的病例:")
        print("=" * 60)
        for i, failed_case in enumerate(failed_cases, 1):
            print(f"   {i:2d}. {failed_case['case_name']}: {failed_case['error']}")
        print(f"\n⚠️  共有 {len(failed_cases)} 个病例处理失败，请检查上述错误信息")
    else:
        print(f"\n✅ 所有病例处理成功！")

    # 保存处理日志
    log_file = os.path.join(args.output_dir, 'processing_log_sitk.txt')
    with open(log_file, 'w') as f:
        filter_mode = "ROI过滤模式" if args.roi_filter else "保留所有切片"
        f.write(f"Liver数据集2D预处理日志 - {filter_mode} (使用SimpleITK)\n")
        f.write("=" * 60 + "\n")
        f.write(f"HU窗口: [{args.hu_min}, {args.hu_max}]\n")
        f.write(f"目标尺寸: {args.size}×{args.size}\n")
        f.write(f"目标Z轴spacing: {args.spacing_z} mm\n")
        f.write(f"并行进程数: {args.num_processes}\n")
        f.write(f"ROI过滤: {'启用' if args.roi_filter else '禁用'}\n")
        if args.roi_filter:
            f.write(f"ROI阈值: {args.roi_threshold}\n")
        f.write(f"处理病例数: {len(all_results)}\n")
        f.write(f"总切片数: {total_processed}\n")
        f.write(f"保存切片数: {total_saved}\n")
        if args.roi_filter:
            f.write(f"过滤切片数: {total_filtered}\n")
            save_ratio = (total_saved / total_processed * 100) if total_processed > 0 else 0
            f.write(f"保存比例: {save_ratio:.1f}%\n")
        else:
            f.write(f"保存比例: 100.0%\n")
        f.write(f"处理时间: {processing_time:.2f} 秒\n\n")

        for result in all_results:
            f.write(f"{result['case_name']}: {result['total_slices']} 切片")
            if result.get('roi_filter_enabled', False):
                f.write(f" -> 保存 {result['saved_count']} 切片，过滤 {result['filtered_count']} 切片\n")
            else:
                f.write(f" -> 全部保存\n")
            f.write(f"  原始形状: {result['original_shape']}\n")
            f.write(f"  重采样后形状: {result['resampled_shape']}\n")
            f.write(f"  原始spacing: {result['original_spacing']}\n")
            f.write(f"  目标Z轴spacing: {result['target_spacing_z']}\n")
            if result.get('roi_filter_enabled', False):
                f.write(f"  ROI阈值: {result['roi_threshold']}\n")
            f.write("\n")

        if failed_cases:
            f.write("\n失败病例:\n")
            for failed_case in failed_cases:
                f.write(f"{failed_case['case_name']}: {failed_case['error']}\n")

    print(f"📝 处理日志已保存到: {log_file}")

if __name__ == "__main__":
    # 多进程保护
    import multiprocessing
    multiprocessing.set_start_method('spawn', force=True)
    main()
