#!/usr/bin/env python3
"""
Process Synapse dataset from npy files to h5 slices format.
Convert 3D volumes to individual slice h5 files with image and label data.
"""

import os
import numpy as np
import h5py
from pathlib import Path
import glob
from tqdm import tqdm

def process_synapse_to_slices():
    """
    Process Synapse npy files to h5 slice format.
    Input: /home/<USER>/data/Synapse/npy/*.npy
    Output: /home/<USER>/data/Synapse/h5_slices/case{case_id}_slice{slice_id:03d}.h5
    """
    
    # Define paths
    input_dir = "/home/<USER>/data/Synapse/npy"
    output_dir = "/home/<USER>/data/Synapse/h5_slices"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all image files
    image_files = sorted(glob.glob(os.path.join(input_dir, "*_image.npy")))
    
    slice_list = []
    
    print(f"Found {len(image_files)} image files to process")
    
    for image_file in tqdm(image_files, desc="Processing cases"):
        # Extract case ID from filename
        case_id = os.path.basename(image_file).split('_')[0]
        
        # Corresponding label file
        label_file = image_file.replace('_image.npy', '_label.npy')
        
        if not os.path.exists(label_file):
            print(f"Warning: Label file not found for {image_file}")
            continue
            
        # Load image and label data
        print(f"Loading case {case_id}...")
        image_data = np.load(image_file)
        label_data = np.load(label_file)
        
        print(f"Image shape: {image_data.shape}, Label shape: {label_data.shape}")
        
        # Ensure both have same shape
        if image_data.shape != label_data.shape:
            print(f"Warning: Shape mismatch for case {case_id}")
            print(f"Image: {image_data.shape}, Label: {label_data.shape}")
            continue
            
        # Process each slice
        num_slices = image_data.shape[0]
        
        for slice_idx in range(num_slices):
            # Create slice filename
            slice_name = f"case{case_id}_slice{slice_idx:03d}"
            h5_filename = f"{slice_name}.h5"
            h5_path = os.path.join(output_dir, h5_filename)
            
            # Extract slice data
            image_slice = image_data[slice_idx]  # Shape: (512, 512)
            label_slice = label_data[slice_idx]  # Shape: (512, 512)
            
            # Save to h5 file
            with h5py.File(h5_path, 'w') as h5f:
                h5f.create_dataset('image', data=image_slice, compression='gzip')
                h5f.create_dataset('label', data=label_slice, compression='gzip')
            
            # Add to slice list
            slice_list.append(slice_name)
        
        print(f"Processed case {case_id}: {num_slices} slices")
    
    # Save slice list
    list_file = "/home/<USER>/data/Synapse/slices.list"
    with open(list_file, 'w') as f:
        for slice_name in slice_list:
            f.write(f"{slice_name}\n")
    
    print(f"\nProcessing complete!")
    print(f"Total slices processed: {len(slice_list)}")
    print(f"Output directory: {output_dir}")
    print(f"Slice list saved to: {list_file}")
    
    return slice_list

if __name__ == "__main__":
    slice_list = process_synapse_to_slices()
    print(f"Generated {len(slice_list)} slice files")
