import SimpleITK as sitk
import numpy as np

image_path = '/home/<USER>/data/MSWAL/imagesTr/MSWAL_0001_0000.nii.gz'
label_path = "/home/<USER>/data/MSWAL/labelsTr/MSWAL_0001.nii.gz"

img_sitk = sitk.ReadImage(image_path)
label_sitk = sitk.ReadImage(label_path)

# 转换为numpy数组
img_data = sitk.GetArrayFromImage(img_sitk)
label_data = sitk.GetArrayFromImage(label_sitk)

# SimpleITK的数组是(Z, Y, X)格式，需要转置为(X, Y, Z)
img_data = np.transpose(img_data, (2, 1, 0))
label_data = np.transpose(label_data, (2, 1, 0))
