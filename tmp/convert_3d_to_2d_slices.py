#!/usr/bin/env python3
"""
Convert 3D H5 files to 2D slice format.
Convert test_vol_h5_new 3D volumes to individual 2D slice H5 files.
"""

import os
import numpy as np
import h5py
import glob
from tqdm import tqdm
from pathlib import Path

def convert_3d_to_2d_slices():
    """
    Convert 3D H5 files to 2D slice format.
    Input: /home/<USER>/data/synapse/test_vol_h5_new/*.h5
    Output: /home/<USER>/data/synapse/val_2d/case{case_id}_slice{slice_id:03d}.h5
    """
    
    # Define paths
    input_dir = "/home/<USER>/data/synapse/test_vol_h5_new"
    output_dir = "/home/<USER>/data/synapse/val_2d"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all 3D H5 files
    h5_files = sorted(glob.glob(os.path.join(input_dir, "*.h5")))
    
    print(f"Found {len(h5_files)} 3D H5 files")
    
    if len(h5_files) == 0:
        print("No H5 files found")
        return
    
    slice_list = []
    successful_conversions = 0
    failed_conversions = 0
    
    print("Starting conversion...")
    
    for h5_file in tqdm(h5_files, desc="Converting 3D to 2D slices"):
        try:
            # Extract case ID from filename (e.g., case0001.npy.h5 -> 0001)
            base_name = os.path.basename(h5_file)
            if base_name.startswith('case') and base_name.endswith('.npy.h5'):
                case_id = base_name[4:8]  # Extract 4 digits after 'case'
            elif base_name.startswith('case') and base_name.endswith('.h5'):
                case_id = base_name[4:8]  # Extract 4 digits after 'case'
            else:
                print(f"Warning: Unexpected filename format: {base_name}")
                failed_conversions += 1
                continue
            
            # Load 3D H5 file
            with h5py.File(h5_file, 'r') as f:
                if 'image' not in f or 'label' not in f:
                    print(f"Warning: Missing image or label in {base_name}")
                    failed_conversions += 1
                    continue
                
                image_3d = f['image'][:]
                label_3d = f['label'][:]
                
                print(f"Processing case{case_id}: shape {image_3d.shape}")
                
                # Ensure both have same shape
                if image_3d.shape != label_3d.shape:
                    print(f"Warning: Shape mismatch for case{case_id}")
                    print(f"Image: {image_3d.shape}, Label: {label_3d.shape}")
                    failed_conversions += 1
                    continue
                
                # Process each slice
                num_slices = image_3d.shape[0]
                
                for slice_idx in range(num_slices):
                    # Create slice filename
                    slice_name = f"case{case_id}_slice{slice_idx:03d}"
                    h5_filename = f"{slice_name}.h5"
                    h5_path = os.path.join(output_dir, h5_filename)
                    
                    # Extract slice data
                    image_slice = image_3d[slice_idx]  # Shape: (512, 512)
                    label_slice = label_3d[slice_idx]  # Shape: (512, 512)
                    
                    # Save to H5 file
                    with h5py.File(h5_path, 'w') as h5f:
                        h5f.create_dataset('image', data=image_slice, compression='gzip', compression_opts=9)
                        h5f.create_dataset('label', data=label_slice, compression='gzip', compression_opts=9)
                        
                        # Add metadata
                        h5f.attrs['source_file'] = base_name
                        h5f.attrs['case_id'] = case_id
                        h5f.attrs['slice_index'] = slice_idx
                        h5f.attrs['image_shape'] = image_slice.shape
                        h5f.attrs['label_shape'] = label_slice.shape
                        h5f.attrs['image_dtype'] = str(image_slice.dtype)
                        h5f.attrs['label_dtype'] = str(label_slice.dtype)
                        h5f.attrs['dataset'] = 'Synapse_test_vol'
                    
                    # Add to slice list
                    slice_list.append(slice_name)
                
                print(f"Processed case{case_id}: {num_slices} slices")
                successful_conversions += 1
                
        except Exception as e:
            print(f"Error processing {h5_file}: {e}")
            failed_conversions += 1
            continue
    
    # Save slice list to test_vol_2d.txt
    list_file = "/home/<USER>/data/synapse/test_vol_2d.txt"
    with open(list_file, 'w') as f:
        for slice_name in slice_list:
            f.write(f"{slice_name}\n")
    
    # Output conversion results
    print(f"\n=== Conversion Complete ===")
    print(f"Successful conversions: {successful_conversions} files")
    print(f"Failed conversions: {failed_conversions} files")
    print(f"Total slices generated: {len(slice_list)}")
    print(f"Output directory: {output_dir}")
    print(f"Slice list saved to: {list_file}")
    
    # Verify some converted files
    if len(slice_list) > 0:
        print(f"\nVerifying conversion results...")
        h5_files_2d = sorted(glob.glob(os.path.join(output_dir, "*.h5")))
        
        # Check first few files
        for i, h5_file in enumerate(h5_files_2d[:3]):
            try:
                with h5py.File(h5_file, 'r') as f:
                    print(f"{i+1}. {os.path.basename(h5_file)}:")
                    print(f"   Keys: {list(f.keys())}")
                    print(f"   Image shape: {f['image'].shape}, dtype: {f['image'].dtype}")
                    print(f"   Label shape: {f['label'].shape}, dtype: {f['label'].dtype}")
                    
                    if 'case_id' in f.attrs:
                        print(f"   Case ID: {f.attrs['case_id']}")
                    if 'slice_index' in f.attrs:
                        print(f"   Slice index: {f.attrs['slice_index']}")
                        
            except Exception as e:
                print(f"Error verifying {h5_file}: {e}")
    
    return successful_conversions, failed_conversions, len(slice_list)

if __name__ == "__main__":
    success, failed, total_slices = convert_3d_to_2d_slices()
    print(f"\nConversion summary: {success} files successful, {failed} failed, {total_slices} slices generated")
