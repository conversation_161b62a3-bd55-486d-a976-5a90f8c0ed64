#!/usr/bin/env python3
"""
检查H5文件中的NaN和Inf值
"""

import h5py
import numpy as np
import os
import glob
from pathlib import Path

def check_h5_file(file_path):
    """检查单个H5文件中的NaN和Inf值"""
    problems = []
    
    try:
        with h5py.File(file_path, 'r') as f:
            # 检查文件中的数据集
            datasets = list(f.keys())
            print(f"检查文件: {file_path}")
            print(f"数据集: {datasets}")
            
            for dataset_name in ['image', 'label']:
                if dataset_name in f:
                    data = f[dataset_name][:]
                    
                    # 检查数据类型和形状
                    print(f"  {dataset_name}: shape={data.shape}, dtype={data.dtype}")
                    
                    # 检查NaN值
                    nan_count = np.isnan(data).sum()
                    if nan_count > 0:
                        problems.append(f"{dataset_name}: {nan_count} NaN values")
                        print(f"    发现 {nan_count} 个NaN值")
                    
                    # 检查Inf值
                    inf_count = np.isinf(data).sum()
                    if inf_count > 0:
                        problems.append(f"{dataset_name}: {inf_count} Inf values")
                        print(f"    发现 {inf_count} 个Inf值")
                    
                    # 检查数据范围
                    if data.size > 0:
                        data_min = np.min(data)
                        data_max = np.max(data)
                        data_mean = np.mean(data)
                        data_std = np.std(data)
                        print(f"    数据范围: [{data_min:.6f}, {data_max:.6f}]")
                        print(f"    均值: {data_mean:.6f}, 标准差: {data_std:.6f}")
                        
                        # 检查异常大的值
                        if np.abs(data_max) > 1e6 or np.abs(data_min) > 1e6:
                            problems.append(f"{dataset_name}: 异常大的值 (min={data_min}, max={data_max})")
                            print(f"    警告: 发现异常大的值")
                else:
                    problems.append(f"缺少数据集: {dataset_name}")
                    print(f"  警告: 缺少数据集 {dataset_name}")
            
            print()
            
    except Exception as e:
        problems.append(f"读取文件错误: {str(e)}")
        print(f"错误: 无法读取文件 {file_path}: {str(e)}")
    
    return problems

def main():
    """主函数"""
    data_dir = "tumor/Dataset017_Liver/h5_1"
    
    if not os.path.exists(data_dir):
        print(f"错误: 数据目录不存在: {data_dir}")
        return
    
    # 获取所有H5文件
    h5_files = glob.glob(os.path.join(data_dir, "*.h5"))
    h5_files.sort()
    
    print(f"找到 {len(h5_files)} 个H5文件")
    print("=" * 60)
    
    all_problems = {}
    total_files_checked = 0
    files_with_problems = 0
    
    # 检查每个文件
    for file_path in h5_files:
        problems = check_h5_file(file_path)
        total_files_checked += 1
        
        if problems:
            all_problems[file_path] = problems
            files_with_problems += 1
    
    # 输出总结
    print("=" * 60)
    print("检查总结:")
    print(f"总共检查文件数: {total_files_checked}")
    print(f"有问题的文件数: {files_with_problems}")
    
    if all_problems:
        print("\n发现的问题:")
        for file_path, problems in all_problems.items():
            print(f"\n文件: {file_path}")
            for problem in problems:
                print(f"  - {problem}")
    else:
        print("\n✓ 所有文件都没有发现NaN或Inf值问题")
    
    # 保存问题报告
    report_file = "tmp/h5_check_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("H5文件检查报告\n")
        f.write("=" * 60 + "\n")
        f.write(f"检查时间: {os.popen('date').read().strip()}\n")
        f.write(f"数据目录: {data_dir}\n")
        f.write(f"总共检查文件数: {total_files_checked}\n")
        f.write(f"有问题的文件数: {files_with_problems}\n\n")
        
        if all_problems:
            f.write("发现的问题:\n")
            for file_path, problems in all_problems.items():
                f.write(f"\n文件: {file_path}\n")
                for problem in problems:
                    f.write(f"  - {problem}\n")
        else:
            f.write("✓ 所有文件都没有发现NaN或Inf值问题\n")
    
    print(f"\n详细报告已保存到: {report_file}")

if __name__ == "__main__":
    main()
