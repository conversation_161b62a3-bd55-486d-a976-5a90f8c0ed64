#!/usr/bin/env python3
"""
可视化ACDC数据集的SLIC超像素分割结果
显示原始图像、分割掩码和带有分割ID的可视化结果
"""

import pickle
import torch
import h5py
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
import os
import argparse
from pathlib import Path

def load_h5_data(h5_path):
    """加载H5文件中的图像和标签数据"""
    with h5py.File(h5_path, 'r') as f:
        # 打印文件中的所有键
        print(f"H5文件中的键: {list(f.keys())}")
        
        # 通常医学图像数据的键名
        possible_image_keys = ['image', 'data', 'img', 'volume']
        possible_label_keys = ['label', 'mask', 'seg', 'segmentation', 'gt']
        
        image_data = None
        label_data = None
        
        # 查找图像数据
        for key in possible_image_keys:
            if key in f:
                image_data = f[key][:]
                print(f"找到图像数据，键名: {key}, 形状: {image_data.shape}")
                break
        
        # 查找标签数据
        for key in possible_label_keys:
            if key in f:
                label_data = f[key][:]
                print(f"找到标签数据，键名: {key}, 形状: {label_data.shape}")
                break
        
        # 如果没有找到，使用第一个可用的键
        if image_data is None:
            keys = list(f.keys())
            if keys:
                image_data = f[keys[0]][:]
                print(f"使用第一个键作为图像数据: {keys[0]}, 形状: {image_data.shape}")
        
        return image_data, label_data

def load_slic_segments(pt_path):
    """加载PyTorch格式的SLIC分割结果"""
    segments = torch.load(pt_path, map_location='cpu')
    if isinstance(segments, torch.Tensor):
        segments = segments.numpy()
    print(f"SLIC分割形状: {segments.shape}")
    print(f"分割ID范围: {segments.min()} - {segments.max()}")
    print(f"唯一分割数量: {len(np.unique(segments))}")
    return segments

def create_segment_colormap(n_segments):
    """创建用于显示分割的颜色映射"""
    # 使用HSV颜色空间生成不同的颜色
    colors = plt.cm.tab20(np.linspace(0, 1, min(n_segments, 20)))
    if n_segments > 20:
        # 如果分割数量超过20，使用更多颜色
        additional_colors = plt.cm.Set3(np.linspace(0, 1, n_segments - 20))
        colors = np.vstack([colors, additional_colors])
    return ListedColormap(colors)

def add_segment_ids_to_plot(ax, segments, show_all_ids=False, max_ids=50):
    """在图像上添加分割ID标注"""
    unique_segments = np.unique(segments)
    
    # 如果分割数量太多，只显示部分ID
    if len(unique_segments) > max_ids and not show_all_ids:
        # 随机选择一些分割ID来显示
        selected_ids = np.random.choice(unique_segments, max_ids, replace=False)
        print(f"分割数量过多({len(unique_segments)})，只显示{max_ids}个ID")
    else:
        selected_ids = unique_segments
    
    for seg_id in selected_ids:
        # 找到该分割的中心位置
        mask = segments == seg_id
        if np.sum(mask) > 0:
            y_coords, x_coords = np.where(mask)
            center_y = int(np.mean(y_coords))
            center_x = int(np.mean(x_coords))
            
            # 在中心位置添加文本标注
            ax.text(center_x, center_y, str(seg_id), 
                   fontsize=8, ha='center', va='center',
                   bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.7),
                   color='black', weight='bold')

def visualize_slic_segments(h5_path, pt_path, output_path=None, show_ids=True, max_ids=50):
    """可视化SLIC超像素分割结果"""
    
    # 加载数据
    print(f"加载H5文件: {h5_path}")
    image_data, label_data = load_h5_data(h5_path)
    
    print(f"加载SLIC分割文件: {pt_path}")
    segments = load_slic_segments(pt_path)
    
    # 确保图像是2D的
    if image_data.ndim > 2:
        if image_data.shape[0] == 1:
            image_data = image_data[0]
        elif image_data.shape[-1] == 1:
            image_data = image_data[..., 0]
        else:
            # 如果是多通道，取第一个通道
            image_data = image_data[..., 0] if image_data.shape[-1] < image_data.shape[0] else image_data[0]
    
    if label_data is not None and label_data.ndim > 2:
        if label_data.shape[0] == 1:
            label_data = label_data[0]
        elif label_data.shape[-1] == 1:
            label_data = label_data[..., 0]
        else:
            label_data = label_data[..., 0] if label_data.shape[-1] < label_data.shape[0] else label_data[0]
    
    # 确保分割掩码是2D的
    if segments.ndim > 2:
        if segments.shape[0] == 1:
            segments = segments[0]
        elif segments.shape[-1] == 1:
            segments = segments[..., 0]
        else:
            segments = segments[..., 0] if segments.shape[-1] < segments.shape[0] else segments[0]
    
    print(f"处理后的形状 - 图像: {image_data.shape}, 分割: {segments.shape}")
    
    # 创建颜色映射
    n_segments = len(np.unique(segments))
    segment_cmap = create_segment_colormap(n_segments)
    
    # 创建可视化
    if label_data is not None:
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()
    else:
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # 显示原始图像
    axes[0].imshow(image_data, cmap='gray')
    axes[0].set_title('原始图像')
    axes[0].axis('off')
    
    # 显示SLIC分割结果（彩色）
    im1 = axes[1].imshow(segments, cmap=segment_cmap)
    axes[1].set_title(f'SLIC超像素分割 ({n_segments}个分割)')
    axes[1].axis('off')
    
    # 显示带有ID标注的分割结果
    axes[2].imshow(image_data, cmap='gray', alpha=0.7)
    axes[2].imshow(segments, cmap=segment_cmap, alpha=0.5)
    if show_ids:
        add_segment_ids_to_plot(axes[2], segments, max_ids=max_ids)
    axes[2].set_title('原始图像 + 分割边界 + ID标注')
    axes[2].axis('off')
    
    # 如果有标签数据，显示标签
    if label_data is not None:
        axes[3].imshow(label_data, cmap='jet')
        axes[3].set_title('真实标签')
        axes[3].axis('off')
    
    # 添加颜色条
    if label_data is not None:
        plt.colorbar(im1, ax=axes[1], fraction=0.046, pad=0.04)
    else:
        plt.colorbar(im1, ax=axes[1], fraction=0.046, pad=0.04)
    
    plt.tight_layout()
    
    # 保存图像
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"可视化结果已保存到: {output_path}")
    
    plt.show()
    
    # 打印统计信息
    print(f"\n=== 统计信息 ===")
    print(f"图像形状: {image_data.shape}")
    print(f"图像值范围: {image_data.min():.2f} - {image_data.max():.2f}")
    print(f"分割形状: {segments.shape}")
    print(f"分割ID数量: {n_segments}")
    print(f"分割ID范围: {segments.min()} - {segments.max()}")
    
    if label_data is not None:
        print(f"标签形状: {label_data.shape}")
        print(f"标签值范围: {label_data.min()} - {label_data.max()}")
        unique_labels = np.unique(label_data)
        print(f"唯一标签值: {unique_labels}")

def main():
    parser = argparse.ArgumentParser(description='可视化ACDC数据集的SLIC超像素分割结果')
    parser.add_argument('--slic_file', type=str, 
                       default='ACDC/slic_mask_cache/segments_patient003_frame01_slice_1.pt',
                       help='SLIC分割文件路径')
    parser.add_argument('--h5_file', type=str,
                       default='ACDC/data/slices/patient003_frame01_slice_1.h5',
                       help='对应的H5图像文件路径')
    parser.add_argument('--output', type=str,
                       default='tmp/slic_visualization.png',
                       help='输出图像路径')
    parser.add_argument('--show_ids', action='store_true', default=True,
                       help='是否显示分割ID')
    parser.add_argument('--max_ids', type=int, default=50,
                       help='最大显示的ID数量')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.slic_file):
        print(f"错误: SLIC文件不存在: {args.slic_file}")
        return
    
    if not os.path.exists(args.h5_file):
        print(f"错误: H5文件不存在: {args.h5_file}")
        return
    
    # 创建输出目录
    output_dir = os.path.dirname(args.output)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 执行可视化
    visualize_slic_segments(
        h5_path=args.h5_file,
        pt_path=args.slic_file,
        output_path=args.output,
        show_ids=args.show_ids,
        max_ids=args.max_ids
    )

if __name__ == "__main__":
    main()
