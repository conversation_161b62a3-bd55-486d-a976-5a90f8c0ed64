#!/usr/bin/env python3
"""
遍历Synapse train_npz_new文件夹下的所有npz文件，统计label的种类
"""

import os
import numpy as np
import glob
from collections import defaultdict
from tqdm import tqdm

def analyze_synapse_labels():
    """
    分析Synapse数据集中所有npz文件的label种类
    """
    
    # 定义路径
    npz_dir = "/home/<USER>/data/synapse/train_npz_new"
    
    # 获取所有npz文件
    npz_files = sorted(glob.glob(os.path.join(npz_dir, "*.npz")))
    
    print(f"找到 {len(npz_files)} 个npz文件")
    
    if len(npz_files) == 0:
        print("未找到任何npz文件")
        return
    
    # 统计所有唯一标签
    all_unique_labels = set()
    label_counts = defaultdict(int)
    file_label_stats = []
    
    print("开始分析文件...")
    
    for npz_file in tqdm(npz_files, desc="处理npz文件"):
        try:
            # 加载npz文件
            data = np.load(npz_file)
            
            if 'label' not in data:
                print(f"警告: {os.path.basename(npz_file)} 中没有label数据")
                continue
                
            label_data = data['label']
            
            # 获取当前文件的唯一标签
            unique_labels = np.unique(label_data)
            
            # 统计信息
            file_info = {
                'filename': os.path.basename(npz_file),
                'shape': label_data.shape,
                'dtype': str(label_data.dtype),
                'unique_labels': unique_labels.tolist(),
                'num_unique': len(unique_labels)
            }
            file_label_stats.append(file_info)
            
            # 更新全局统计
            for label in unique_labels:
                all_unique_labels.add(label)
                label_counts[label] += np.sum(label_data == label)
                
        except Exception as e:
            print(f"处理文件 {npz_file} 时出错: {e}")
            continue
    
    # 输出结果
    print(f"\n=== 分析结果 ===")
    print(f"总文件数: {len(npz_files)}")
    print(f"成功处理的文件数: {len(file_label_stats)}")
    
    # 全局标签统计
    all_unique_labels = sorted(list(all_unique_labels))
    print(f"\n所有唯一标签值: {all_unique_labels}")
    print(f"标签种类总数: {len(all_unique_labels)}")
    
    # 各标签的像素统计
    print(f"\n各标签的像素统计:")
    total_pixels = sum(label_counts.values())
    for label in all_unique_labels:
        count = label_counts[label]
        percentage = (count / total_pixels) * 100 if total_pixels > 0 else 0
        print(f"标签 {label}: {count:,} 像素 ({percentage:.2f}%)")
    
    # 显示一些文件的详细信息
    print(f"\n前10个文件的标签信息:")
    for i, info in enumerate(file_label_stats[:10]):
        print(f"{i+1}. {info['filename']}")
        print(f"   Shape: {info['shape']}, 唯一标签: {info['unique_labels']}")
    
    # 统计有多少个标签的文件
    files_with_labels = [info for info in file_label_stats if info['num_unique'] > 1]
    files_only_background = [info for info in file_label_stats if info['num_unique'] == 1]
    
    print(f"\n文件标签分布:")
    print(f"只有背景标签(0)的文件: {len(files_only_background)}")
    print(f"包含前景标签的文件: {len(files_with_labels)}")
    
    if len(files_with_labels) > 0:
        print(f"\n包含前景标签的文件示例:")
        for i, info in enumerate(files_with_labels[:5]):
            print(f"{i+1}. {info['filename']}: {info['unique_labels']}")
    
    return all_unique_labels, label_counts, file_label_stats

if __name__ == "__main__":
    unique_labels, counts, stats = analyze_synapse_labels()
