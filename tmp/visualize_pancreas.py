import h5py
import numpy as np
import matplotlib.pyplot as plt
import os

def visualize_pancreas_h5(h5_file_path, output_dir="./tmp/pancreas_visualization1"):
    """
    可视化Pancreas tumor H5文件的切片
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取H5文件
    with h5py.File(h5_file_path, 'r') as f:
        print("H5文件结构:")
        print(f"Keys: {list(f.keys())}")
        
        # 读取image和label数据
        image = f['image'][:]
        label = f['label'][:]
        
        print(f"Image shape: {image.shape}")
        print(f"Label shape: {label.shape}")
        print(f"Image dtype: {image.dtype}")
        print(f"Label dtype: {label.dtype}")
        print(f"Image value range: [{image.min():.2f}, {image.max():.2f}]")
        print(f"Label unique values: {np.unique(label)}")
        
        # 统计每个标签的像素数量
        unique_labels, counts = np.unique(label, return_counts=True)
        print("\n标签统计:")
        for label_val, count in zip(unique_labels, counts):
            percentage = count / label.size * 100
            if label_val == 0:
                print(f"  背景 (0): {count} pixels ({percentage:.2f}%)")
            elif label_val == 1:
                print(f"  胰腺 (1): {count} pixels ({percentage:.2f}%)")
            elif label_val == 2:
                print(f"  肿瘤 (2): {count} pixels ({percentage:.2f}%)")
        
        # 如果是3D数据，取几个代表性切片进行可视化
        if len(image.shape) == 3:
            num_slices = image.shape[0]
            print(f"\n总切片数: {num_slices}")
            
            # 选择几个切片进行可视化
            slice_indices = [
                0,  # 第一个切片
                num_slices // 4,  # 1/4处
                num_slices // 2,  # 中间
                3 * num_slices // 4,  # 3/4处
                num_slices - 1  # 最后一个切片
            ]
            
            for i, slice_idx in enumerate(slice_indices):
                if slice_idx < num_slices:
                    visualize_slice(image[slice_idx], label[slice_idx], slice_idx, 
                                  output_dir, f"slice_{slice_idx:03d}")
        
        elif len(image.shape) == 2:
            # 2D数据直接可视化
            visualize_slice(image, label, 0, output_dir, "slice_000")
        
        # 创建整体统计图
        create_statistics_plot(image, label, output_dir)

def visualize_slice(image_slice, label_slice, slice_idx, output_dir, filename_prefix):
    """
    可视化单个切片
    """
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle(f'Pancreas Data Visualization - Slice {slice_idx}', fontsize=16)
    
    # 原始图像
    im1 = axes[0, 0].imshow(image_slice, cmap='gray')
    axes[0, 0].set_title('Original Image')
    axes[0, 0].axis('off')
    plt.colorbar(im1, ax=axes[0, 0])
    
    # 标签图像
    im2 = axes[0, 1].imshow(label_slice, cmap='viridis', vmin=0, vmax=2)
    axes[0, 1].set_title('Label (0:背景, 1:胰腺, 2:肿瘤)')
    axes[0, 1].axis('off')
    plt.colorbar(im2, ax=axes[0, 1])
    
    # 叠加显示
    axes[1, 0].imshow(image_slice, cmap='gray', alpha=0.7)
    # 创建彩色标签遮罩
    label_colored = np.zeros((*label_slice.shape, 4))
    # 胰腺用绿色
    pancreas_mask = label_slice == 1
    label_colored[pancreas_mask] = [0, 1, 0, 0.5]  # 绿色，半透明
    # 肿瘤用红色
    tumor_mask = label_slice == 2
    label_colored[tumor_mask] = [1, 0, 0, 0.7]  # 红色，更不透明
    
    axes[1, 0].imshow(label_colored)
    axes[1, 0].set_title('Image + Label Overlay\n(绿色:胰腺, 红色:肿瘤)')
    axes[1, 0].axis('off')
    
    # 统计信息
    unique_labels, counts = np.unique(label_slice, return_counts=True)
    stats_text = f"切片 {slice_idx} 统计:\n"
    stats_text += f"图像范围: [{image_slice.min():.2f}, {image_slice.max():.2f}]\n"
    for label_val, count in zip(unique_labels, counts):
        percentage = count / label_slice.size * 100
        if label_val == 0:
            stats_text += f"背景: {percentage:.1f}%\n"
        elif label_val == 1:
            stats_text += f"胰腺: {percentage:.1f}%\n"
        elif label_val == 2:
            stats_text += f"肿瘤: {percentage:.1f}%\n"
    
    axes[1, 1].text(0.1, 0.5, stats_text, transform=axes[1, 1].transAxes, 
                    fontsize=12, verticalalignment='center')
    axes[1, 1].axis('off')
    
    # 保存图像
    output_path = os.path.join(output_dir, f"{filename_prefix}_visualization.png")
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"已保存: {output_path}")

def create_statistics_plot(image, label, output_dir):
    """
    创建整体统计图
    """
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle('Pancreas Dataset Statistics', fontsize=16)
    
    # 图像直方图
    axes[0, 0].hist(image.flatten(), bins=50, alpha=0.7, color='blue')
    axes[0, 0].set_title('Image Intensity Distribution')
    axes[0, 0].set_xlabel('Intensity Value')
    axes[0, 0].set_ylabel('Frequency')
    
    # 标签分布
    unique_labels, counts = np.unique(label, return_counts=True)
    label_names = ['背景', '胰腺', '肿瘤']
    colors = ['gray', 'green', 'red']
    
    bars = axes[0, 1].bar(range(len(unique_labels)), counts, 
                         color=[colors[int(l)] for l in unique_labels])
    axes[0, 1].set_title('Label Distribution')
    axes[0, 1].set_xlabel('Label')
    axes[0, 1].set_ylabel('Pixel Count')
    axes[0, 1].set_xticks(range(len(unique_labels)))
    axes[0, 1].set_xticklabels([f'{int(l)}\n({label_names[int(l)]})' for l in unique_labels])
    
    # 添加数值标签
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        axes[0, 1].text(bar.get_x() + bar.get_width()/2., height,
                       f'{count}\n({count/label.size*100:.1f}%)',
                       ha='center', va='bottom')
    
    # 如果是3D数据，显示每个切片的标签分布
    if len(image.shape) == 3:
        slice_stats = []
        for i in range(image.shape[0]):
            slice_label = label[i]
            pancreas_ratio = np.sum(slice_label == 1) / slice_label.size
            tumor_ratio = np.sum(slice_label == 2) / slice_label.size
            slice_stats.append([pancreas_ratio, tumor_ratio])
        
        slice_stats = np.array(slice_stats)
        
        axes[1, 0].plot(slice_stats[:, 0], label='胰腺比例', color='green')
        axes[1, 0].plot(slice_stats[:, 1], label='肿瘤比例', color='red')
        axes[1, 0].set_title('Label Ratio per Slice')
        axes[1, 0].set_xlabel('Slice Index')
        axes[1, 0].set_ylabel('Ratio')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 显示有效切片（包含胰腺或肿瘤的切片）
        valid_slices = np.where((slice_stats[:, 0] > 0) | (slice_stats[:, 1] > 0))[0]
        axes[1, 1].bar(range(len(valid_slices)), valid_slices, alpha=0.7)
        axes[1, 1].set_title(f'Valid Slices (含胰腺/肿瘤): {len(valid_slices)}/{len(slice_stats)}')
        axes[1, 1].set_xlabel('Valid Slice Index')
        axes[1, 1].set_ylabel('Original Slice Index')
    else:
        axes[1, 0].axis('off')
        axes[1, 1].axis('off')
    
    # 保存统计图
    output_path = os.path.join(output_dir, "dataset_statistics.png")
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"已保存统计图: {output_path}")

if __name__ == "__main__":
    # 检查两个文件
    files_to_check = [
        "tumor/Dataset016_Pancreas/h5/pancreas_001.h5",
        "Pancreas/Pancreas_data/PANCREAS_0001.h5"
    ]

    for h5_file_path in files_to_check:
        print(f"\n开始可视化文件: {h5_file_path}")
        try:
            output_dir = f"./tmp/pancreas_visualization_{h5_file_path.replace('/', '_').replace('.h5', '')}"
            visualize_pancreas_h5(h5_file_path, output_dir)
            print(f"可视化完成: {h5_file_path}")
        except Exception as e:
            print(f"处理文件 {h5_file_path} 时出错: {e}")
    print("\n所有可视化完成！")
