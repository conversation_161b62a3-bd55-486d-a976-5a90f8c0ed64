import h5py
import numpy as np

def check_h5_file_info(h5_file_path):
    """
    检查H5文件的基本信息
    """
    try:
        with h5py.File(h5_file_path, 'r') as f:
            print(f"文件: {h5_file_path}")
            print("=" * 50)
            
            # 显示文件结构
            print("H5文件结构:")
            print(f"Keys: {list(f.keys())}")
            print()
            
            # 检查每个数据集
            for key in f.keys():
                data = f[key]
                print(f"数据集: {key}")
                print(f"  Shape: {data.shape}")
                print(f"  Dtype: {data.dtype}")
                
                # 读取数据来获取统计信息
                data_array = data[:]
                print(f"  数据范围: [{data_array.min():.4f}, {data_array.max():.4f}]")
                print(f"  均值: {data_array.mean():.4f}")
                print(f"  标准差: {data_array.std():.4f}")
                
                if key == 'label':
                    unique_values = np.unique(data_array)
                    print(f"  唯一值: {unique_values}")
                    
                    # 统计每个标签的数量
                    unique_labels, counts = np.unique(data_array, return_counts=True)
                    print("  标签统计:")
                    for label_val, count in zip(unique_labels, counts):
                        percentage = count / data_array.size * 100
                        print(f"    标签 {label_val}: {count} pixels ({percentage:.2f}%)")
                
                print()
                
    except Exception as e:
        print(f"读取文件时出错: {e}")

if __name__ == "__main__":
    h5_file_path = "tumor/Dataset016_Pancreas/h5/pancreas_001.h5"
    check_h5_file_info(h5_file_path)
