#!/usr/bin/env python3
"""
Script to generate new train/test split lists based on msd_liver.json
- Training set: 2D slices from h5_2d directory
- Test set: 3D volumes from h5_2d_val directory
"""

import json
import os
import glob
from pathlib import Path

def extract_case_number(filename):
    """Extract case number from filename like liver_X.nii.gz"""
    basename = os.path.basename(filename)
    # Remove .nii.gz extension and extract number
    case_name = basename.replace('.nii.gz', '')
    case_num = case_name.split('_')[1]
    return int(case_num)

def find_2d_slices(case_num, h5_2d_dir):
    """Find all 2D slices for a given case number"""
    pattern = f"liver_{case_num}_*.h5"
    slice_files = glob.glob(os.path.join(h5_2d_dir, pattern))
    return sorted(slice_files)

def find_3d_volume(case_num, h5_2d_val_dir):
    """Find 3D volume file for a given case number"""
    volume_file = os.path.join(h5_2d_val_dir, f"liver_{case_num}.h5")
    if os.path.exists(volume_file):
        return volume_file
    return None

def main():
    # Paths
    base_dir = "/home/<USER>/data"
    json_file = os.path.join(base_dir, "msd_liver.json")
    h5_2d_dir = os.path.join(base_dir, "tumor/Dataset017_Liver/h5_2d")
    h5_2d_val_dir = os.path.join(base_dir, "tumor/Dataset017_Liver/h5_2d_val")
    
    output_dir = os.path.join(base_dir, "tumor/split")
    train_output = os.path.join(output_dir, "2d_train_tfh.list")
    test_output = os.path.join(output_dir, "2d_test_tfh.list")
    
    # Load JSON file
    with open(json_file, 'r') as f:
        data = json.load(f)
    
    # Extract case numbers from training and validation sets
    train_cases = []
    for item in data['training']:
        case_num = extract_case_number(item['image'])
        train_cases.append(case_num)
    
    val_cases = []
    for item in data['validation']:
        case_num = extract_case_number(item['image'])
        val_cases.append(case_num)
    
    print(f"Training cases: {len(train_cases)} cases")
    print(f"Validation cases: {len(val_cases)} cases")
    
    # Generate training list (2D slices)
    train_files = []
    for case_num in train_cases:
        slice_files = find_2d_slices(case_num, h5_2d_dir)
        train_files.extend(slice_files)
    
    print(f"Found {len(train_files)} training 2D slices")
    
    # Generate test list (3D volumes)
    test_files = []
    for case_num in val_cases:
        volume_file = find_3d_volume(case_num, h5_2d_val_dir)
        if volume_file:
            test_files.append(volume_file)
        else:
            print(f"Warning: 3D volume not found for case {case_num}")
    
    print(f"Found {len(test_files)} test 3D volumes")
    
    # Write training list
    with open(train_output, 'w') as f:
        for file_path in sorted(train_files):
            f.write(file_path + '\n')
    
    # Write test list
    with open(test_output, 'w') as f:
        for file_path in sorted(test_files):
            f.write(file_path + '\n')
    
    print(f"Generated {train_output}")
    print(f"Generated {test_output}")
    
    # Print some statistics
    print(f"\nStatistics:")
    print(f"Training 2D slices: {len(train_files)}")
    print(f"Test 3D volumes: {len(test_files)}")
    
    # Show first few entries of each list
    print(f"\nFirst 5 training entries:")
    for i, file_path in enumerate(sorted(train_files)[:5]):
        print(f"  {i+1}: {file_path}")
    
    print(f"\nFirst 5 test entries:")
    for i, file_path in enumerate(sorted(test_files)[:5]):
        print(f"  {i+1}: {file_path}")

if __name__ == "__main__":
    main()
