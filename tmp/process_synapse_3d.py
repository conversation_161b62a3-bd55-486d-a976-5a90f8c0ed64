#!/usr/bin/env python3
"""
Process Synapse dataset from npy files to 3D h5 format.
Convert 3D volumes to individual 3D h5 files with image and label data.
"""

import os
import numpy as np
import h5py
from pathlib import Path
import glob
from tqdm import tqdm

def process_synapse_to_3d():
    """
    Process Synapse npy files to 3D h5 format.
    Input: /home/<USER>/data/Synapse/npy/*.npy
    Output: /home/<USER>/data/Synapse/h5_3d/case{case_id}.h5
    """
    
    # Define paths
    input_dir = "/home/<USER>/data/Synapse/npy"
    output_dir = "/home/<USER>/data/Synapse/h5_3d"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all image files
    image_files = sorted(glob.glob(os.path.join(input_dir, "*_image.npy")))
    
    case_list = []
    
    print(f"Found {len(image_files)} image files to process")
    
    for image_file in tqdm(image_files, desc="Processing 3D cases"):
        # Extract case ID from filename
        case_id = os.path.basename(image_file).split('_')[0]
        
        # Corresponding label file
        label_file = image_file.replace('_image.npy', '_label.npy')
        
        if not os.path.exists(label_file):
            print(f"Warning: Label file not found for {image_file}")
            continue
            
        # Load image and label data
        print(f"Loading case {case_id}...")
        image_data = np.load(image_file)
        label_data = np.load(label_file)
        
        print(f"Image shape: {image_data.shape}, Label shape: {label_data.shape}")
        
        # Ensure both have same shape
        if image_data.shape != label_data.shape:
            print(f"Warning: Shape mismatch for case {case_id}")
            print(f"Image: {image_data.shape}, Label: {label_data.shape}")
            continue
            
        # Create 3D h5 filename
        case_name = f"case{case_id}"
        h5_filename = f"{case_name}.h5"
        h5_path = os.path.join(output_dir, h5_filename)
        
        # Save to h5 file with 3D data
        with h5py.File(h5_path, 'w') as h5f:
            h5f.create_dataset('image', data=image_data, compression='gzip')
            h5f.create_dataset('label', data=label_data, compression='gzip')
        
        # Add to case list
        case_list.append(case_name)
        
        print(f"Processed case {case_id}: shape {image_data.shape}")
    
    # Save case list
    list_file = "/home/<USER>/data/Synapse/val_3d.list"
    with open(list_file, 'w') as f:
        for case_name in case_list:
            f.write(f"{case_name}\n")
    
    print(f"\n3D Processing complete!")
    print(f"Total cases processed: {len(case_list)}")
    print(f"Output directory: {output_dir}")
    print(f"Case list saved to: {list_file}")
    
    return case_list

if __name__ == "__main__":
    case_list = process_synapse_to_3d()
    print(f"Generated {len(case_list)} 3D case files")
