#!/usr/bin/env python3
"""
<PERSON><PERSON>t to analyze MSWAL dataset and count how many samples contain each label type.
Based on the dataset.json file structure and label definitions.
"""

import json
import os
import SimpleITK as sitk
import numpy as np
from collections import defaultdict
import glob
from tqdm import tqdm

def load_dataset_info(json_file):
    """Load dataset information from JSON file"""
    with open(json_file, 'r') as f:
        data = json.load(f)
    return data

def analyze_label_file(label_path, label_mapping):
    print(label_path)
    """Analyze a single label file and return which labels are present"""
    try:
        # Load the NIfTI file using SimpleITK
        sitk_img = sitk.ReadImage(label_path)
        label_data = sitk.GetArrayFromImage(sitk_img)

        # Get unique values in the label file
        unique_values = np.unique(label_data)
        
        # Map unique values to label names
        present_labels = []
        for value in unique_values:
            value = int(value)
            if value in label_mapping:
                present_labels.append((value, label_mapping[value]))
        
        return present_labels
    except Exception as e:
        print(f"Error processing {label_path}: {e}")
        return []

def main():
    # Paths
    base_dir = "/home/<USER>/data"
    json_file = os.path.join(base_dir, "MSWAL/dataset.json")
    labels_dir = os.path.join(base_dir, "MSWAL/labelsTr")
    
    # Load dataset information
    dataset_info = load_dataset_info(json_file)
    
    # Extract label mapping
    label_mapping = {}
    for label_name, label_value in dataset_info['labels'].items():
        label_mapping[label_value] = label_name
    
    print("MSWAL Dataset Label Analysis")
    print("=" * 50)
    print(f"Dataset: {dataset_info['name']}")
    print(f"Description: {dataset_info['description']}")
    print(f"Total training samples: {dataset_info['numTraining']}")
    print(f"Total test samples: {dataset_info['numTest']}")
    print()
    
    print("Label Definitions:")
    for label_name, label_value in dataset_info['labels'].items():
        print(f"  {label_value}: {label_name}")
    print()
    
    # Initialize counters
    label_counts = defaultdict(int)
    total_files_processed = 0
    
    # Get all label files from the training set
    training_labels = []
    for item in dataset_info['training']:
        label_file = item['label'].replace('./', '')
        label_path = os.path.join(base_dir, "MSWAL", label_file)
        training_labels.append(label_path)
    
    print(f"Processing {len(training_labels)} training label files...")
    print()

    # Process each label file with progress bar
    for label_path in tqdm(training_labels, desc="Analyzing label files", unit="file"):
        if os.path.exists(label_path):
            present_labels = analyze_label_file(label_path, label_mapping)

            # Count each present label (excluding background)
            for label_value, label_name in present_labels:
                if label_value != 0:  # Skip background
                    label_counts[label_name] += 1

            total_files_processed += 1
        else:
            tqdm.write(f"Warning: Label file not found: {label_path}")
    
    print(f"\nProcessed {total_files_processed} label files")
    print()
    
    # Display results
    print("Label Statistics (Number of samples containing each label):")
    print("=" * 60)
    
    # Sort by label value for consistent ordering
    sorted_labels = []
    for label_value, label_name in sorted(label_mapping.items()):
        if label_value != 0:  # Skip background
            count = label_counts[label_name]
            percentage = (count / total_files_processed) * 100 if total_files_processed > 0 else 0
            sorted_labels.append((label_value, label_name, count, percentage))
    
    for label_value, label_name, count, percentage in sorted_labels:
        print(f"{label_value}: {label_name:<20} - {count:4d} samples ({percentage:5.1f}%)")
    
    print()
    print("Summary:")
    print(f"Total training samples analyzed: {total_files_processed}")
    print(f"Background (label 0) is present in all samples by definition")
    
    # Find most and least common labels (excluding background)
    if sorted_labels:
        most_common = max(sorted_labels, key=lambda x: x[2])
        least_common = min(sorted_labels, key=lambda x: x[2])
        
        print(f"Most common lesion type: {most_common[1]} ({most_common[2]} samples)")
        print(f"Least common lesion type: {least_common[1]} ({least_common[2]} samples)")

if __name__ == "__main__":
    main()
