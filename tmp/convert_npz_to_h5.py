#!/usr/bin/env python3
"""
将Synapse train_npz_new文件夹下的npz文件转换为h5格式
"""

import os
import numpy as np
import h5py
import glob
from tqdm import tqdm
from pathlib import Path

def convert_npz_to_h5():
    """
    将npz文件转换为h5格式
    Input: /home/<USER>/data/synapse/train_npz_new/*.npz
    Output: /home/<USER>/data/synapse/train_h5/*.h5
    """
    
    # 定义路径
    input_dir = "/home/<USER>/data/synapse/train_npz_new"
    output_dir = "/home/<USER>/data/synapse/train_h5"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有npz文件
    npz_files = sorted(glob.glob(os.path.join(input_dir, "*.npz")))
    
    print(f"找到 {len(npz_files)} 个npz文件")
    
    if len(npz_files) == 0:
        print("未找到任何npz文件")
        return
    
    successful_conversions = 0
    failed_conversions = 0
    
    print("开始转换文件...")
    
    for npz_file in tqdm(npz_files, desc="转换npz到h5"):
        try:
            # 获取文件名（不含扩展名）
            base_name = os.path.splitext(os.path.basename(npz_file))[0]
            h5_filename = f"{base_name}.h5"
            h5_path = os.path.join(output_dir, h5_filename)
            
            # 加载npz文件
            npz_data = np.load(npz_file)
            
            # 检查必要的键是否存在
            if 'image' not in npz_data or 'label' not in npz_data:
                print(f"警告: {base_name}.npz 缺少必要的数据键")
                failed_conversions += 1
                continue
            
            # 获取数据
            image_data = npz_data['image']
            label_data = npz_data['label']
            
            # 保存为h5文件
            with h5py.File(h5_path, 'w') as h5f:
                # 创建数据集，使用gzip压缩
                h5f.create_dataset('image', data=image_data, compression='gzip', compression_opts=9)
                h5f.create_dataset('label', data=label_data, compression='gzip', compression_opts=9)
                
                # 添加一些元数据
                h5f.attrs['source_file'] = base_name + '.npz'
                h5f.attrs['image_shape'] = image_data.shape
                h5f.attrs['label_shape'] = label_data.shape
                h5f.attrs['image_dtype'] = str(image_data.dtype)
                h5f.attrs['label_dtype'] = str(label_data.dtype)
            
            successful_conversions += 1
            
        except Exception as e:
            print(f"转换文件 {npz_file} 时出错: {e}")
            failed_conversions += 1
            continue
    
    # 输出转换结果
    print(f"\n=== 转换完成 ===")
    print(f"成功转换: {successful_conversions} 个文件")
    print(f"转换失败: {failed_conversions} 个文件")
    print(f"输出目录: {output_dir}")
    
    # 验证一些转换后的文件
    if successful_conversions > 0:
        print(f"\n验证转换结果...")
        h5_files = sorted(glob.glob(os.path.join(output_dir, "*.h5")))
        
        # 检查前几个文件
        for i, h5_file in enumerate(h5_files[:3]):
            try:
                with h5py.File(h5_file, 'r') as f:
                    print(f"{i+1}. {os.path.basename(h5_file)}:")
                    print(f"   Keys: {list(f.keys())}")
                    print(f"   Image shape: {f['image'].shape}, dtype: {f['image'].dtype}")
                    print(f"   Label shape: {f['label'].shape}, dtype: {f['label'].dtype}")
                    if 'source_file' in f.attrs:
                        print(f"   Source: {f.attrs['source_file']}")
            except Exception as e:
                print(f"验证文件 {h5_file} 时出错: {e}")
    
    return successful_conversions, failed_conversions

if __name__ == "__main__":
    success, failed = convert_npz_to_h5()
    print(f"\n转换统计: 成功 {success}, 失败 {failed}")
