#!/usr/bin/env python3
"""
Process ISIC18 dataset from PNG files to H5 format.
Convert PNG images and masks to H5 files with image and label data.
"""

import os
import numpy as np
import h5py
import glob
from PIL import Image
from tqdm import tqdm
from pathlib import Path

def process_isic18_to_h5():
    """
    Process ISIC18 PNG files to H5 format.
    Input: 
        - /home/<USER>/data/isic18/images/*.png
        - /home/<USER>/data/isic18/masks/0/*.png
    Output: /home/<USER>/data/isic18/h5/*.h5
    """
    
    # Define paths
    images_dir = "/home/<USER>/data/isic18/images"
    masks_dir = "/home/<USER>/data/isic18/masks/0"
    output_dir = "/home/<USER>/data/isic18/h5"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all image files
    image_files = sorted(glob.glob(os.path.join(images_dir, "*.png")))
    
    # Filter out .baiduyun.uploading.cfg files
    image_files = [f for f in image_files if not f.endswith('.baiduyun.uploading.cfg')]
    
    print(f"Found {len(image_files)} image files")
    
    if len(image_files) == 0:
        print("No image files found")
        return
    
    successful_conversions = 0
    failed_conversions = 0
    
    print("Starting conversion...")
    
    for image_file in tqdm(image_files, desc="Converting PNG to H5"):
        try:
            # Extract filename without extension
            base_name = os.path.splitext(os.path.basename(image_file))[0]
            
            # Corresponding mask file
            mask_file = os.path.join(masks_dir, f"{base_name}.png")
            
            if not os.path.exists(mask_file):
                print(f"Warning: Mask file not found for {base_name}")
                failed_conversions += 1
                continue
            
            # Load image and mask
            try:
                # Load image (RGB)
                image = Image.open(image_file)
                if image.mode != 'RGB':
                    image = image.convert('RGB')
                image_array = np.array(image, dtype=np.uint8)
                
                # Load mask (grayscale)
                mask = Image.open(mask_file)
                if mask.mode != 'L':
                    mask = mask.convert('L')
                mask_array = np.array(mask, dtype=np.uint8)
                
                # Verify dimensions match
                if image_array.shape[:2] != mask_array.shape:
                    print(f"Warning: Dimension mismatch for {base_name}")
                    print(f"Image: {image_array.shape}, Mask: {mask_array.shape}")
                    failed_conversions += 1
                    continue
                
            except Exception as e:
                print(f"Error loading images for {base_name}: {e}")
                failed_conversions += 1
                continue
            
            # Create H5 filename
            h5_filename = f"{base_name}.h5"
            h5_path = os.path.join(output_dir, h5_filename)
            
            # Save to H5 file
            with h5py.File(h5_path, 'w') as h5f:
                # Create datasets with compression
                h5f.create_dataset('image', data=image_array, compression='gzip', compression_opts=9)
                h5f.create_dataset('label', data=mask_array, compression='gzip', compression_opts=9)
                
                # Add metadata
                h5f.attrs['source_image'] = base_name + '.png'
                h5f.attrs['source_mask'] = base_name + '.png'
                h5f.attrs['image_shape'] = image_array.shape
                h5f.attrs['label_shape'] = mask_array.shape
                h5f.attrs['image_dtype'] = str(image_array.dtype)
                h5f.attrs['label_dtype'] = str(mask_array.dtype)
                h5f.attrs['dataset'] = 'ISIC18'
            
            successful_conversions += 1
            
        except Exception as e:
            print(f"Error processing {image_file}: {e}")
            failed_conversions += 1
            continue
    
    # Output conversion results
    print(f"\n=== Conversion Complete ===")
    print(f"Successful conversions: {successful_conversions}")
    print(f"Failed conversions: {failed_conversions}")
    print(f"Output directory: {output_dir}")
    
    # Verify some converted files
    if successful_conversions > 0:
        print(f"\nVerifying conversion results...")
        h5_files = sorted(glob.glob(os.path.join(output_dir, "*.h5")))
        
        # Check first few files
        for i, h5_file in enumerate(h5_files[:3]):
            try:
                with h5py.File(h5_file, 'r') as f:
                    print(f"{i+1}. {os.path.basename(h5_file)}:")
                    print(f"   Keys: {list(f.keys())}")
                    print(f"   Image shape: {f['image'].shape}, dtype: {f['image'].dtype}")
                    print(f"   Label shape: {f['label'].shape}, dtype: {f['label'].dtype}")
                    
                    # Check label values
                    label_data = f['label'][:]
                    unique_labels = np.unique(label_data)
                    print(f"   Label unique values: {unique_labels}")
                    
                    if 'dataset' in f.attrs:
                        print(f"   Dataset: {f.attrs['dataset']}")
                        
            except Exception as e:
                print(f"Error verifying {h5_file}: {e}")
    
    return successful_conversions, failed_conversions

if __name__ == "__main__":
    success, failed = process_isic18_to_h5()
    print(f"\nConversion summary: {success} successful, {failed} failed")
