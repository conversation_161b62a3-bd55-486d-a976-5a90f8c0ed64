#!/usr/bin/env python3
"""
Convert H5 file to NII.gz format.
Convert case0001.npy.h5 to NII.gz format for medical image analysis.
"""

import os
import numpy as np
import h5py
import nibabel as nib

def convert_h5_to_nii():
    """
    Convert H5 file to NII.gz format.
    Input: /home/<USER>/data/synapse/test_vol_h5_new/case0001.npy.h5
    Output: /home/<USER>/data/synapse/case0001_image.nii.gz and case0001_label.nii.gz
    """
    
    # Define paths
    input_file = "/home/<USER>/data/synapse/test_vol_h5_new/case0001.npy.h5"
    output_dir = "/home/<USER>/data/synapse"
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file not found: {input_file}")
        return False
    
    try:
        # Load H5 file
        print(f"Loading H5 file: {input_file}")
        with h5py.File(input_file, 'r') as f:
            print(f"Keys in H5 file: {list(f.keys())}")
            
            if 'image' not in f or 'label' not in f:
                print("Error: Missing 'image' or 'label' datasets in H5 file")
                return False
            
            # Load image and label data
            image_data = f['image'][:]
            label_data = f['label'][:]
            
            print(f"Image shape: {image_data.shape}, dtype: {image_data.dtype}")
            print(f"Label shape: {label_data.shape}, dtype: {label_data.dtype}")
            print(f"Image value range: {image_data.min():.6f} to {image_data.max():.6f}")
            print(f"Label unique values: {np.unique(label_data)}")
        
        # Create NIfTI images
        # Note: NIfTI format expects (x, y, z) orientation
        # Our data is likely in (z, y, x) format, so we may need to transpose
        
        # For medical images, we typically want RAS+ orientation
        # Create a simple affine matrix (identity with 1mm spacing)
        affine = np.eye(4)
        affine[0, 0] = 1.0  # x spacing
        affine[1, 1] = 1.0  # y spacing  
        affine[2, 2] = 1.0  # z spacing
        
        # Convert image data
        print("Converting image data to NIfTI format...")
        image_nii = nib.Nifti1Image(image_data, affine)
        image_output_path = os.path.join(output_dir, "case0001_image.nii.gz")
        nib.save(image_nii, image_output_path)
        print(f"Saved image to: {image_output_path}")
        
        # Convert label data
        print("Converting label data to NIfTI format...")
        # Ensure label data is integer type for segmentation
        if label_data.dtype != np.uint8 and label_data.dtype != np.uint16:
            label_data = label_data.astype(np.uint8)
        
        label_nii = nib.Nifti1Image(label_data, affine)
        label_output_path = os.path.join(output_dir, "case0001_label.nii.gz")
        nib.save(label_nii, label_output_path)
        print(f"Saved label to: {label_output_path}")
        
        # Verify the saved files
        print("\nVerifying saved NIfTI files...")
        
        # Check image file
        img_check = nib.load(image_output_path)
        print(f"Image NIfTI shape: {img_check.shape}")
        print(f"Image NIfTI data type: {img_check.get_fdata().dtype}")
        print(f"Image NIfTI affine:\n{img_check.affine}")
        
        # Check label file
        lbl_check = nib.load(label_output_path)
        print(f"Label NIfTI shape: {lbl_check.shape}")
        print(f"Label NIfTI data type: {lbl_check.get_fdata().dtype}")
        print(f"Label unique values: {np.unique(lbl_check.get_fdata())}")
        
        print(f"\nConversion completed successfully!")
        print(f"Output files:")
        print(f"  - {image_output_path}")
        print(f"  - {label_output_path}")
        
        return True
        
    except Exception as e:
        print(f"Error during conversion: {e}")
        return False

if __name__ == "__main__":
    success = convert_h5_to_nii()
    if success:
        print("H5 to NIfTI conversion completed successfully!")
    else:
        print("H5 to NIfTI conversion failed!")
