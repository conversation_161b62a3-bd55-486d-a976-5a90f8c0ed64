#!/usr/bin/env python3
"""
将h5目录下所有文件的绝对路径按名称顺序写入到tmp.list文件中
"""

import os
import re
from pathlib import Path

def get_sort_key(filename):
    """
    生成排序键，确保liver_1.h5在liver_10.h5之前
    """
    # 提取数字部分进行数值排序
    match = re.match(r'liver_(\d+)\.h5', filename)
    if match:
        return int(match.group(1))
    return float('inf')  # 非标准格式的文件排在最后

def create_file_list(h5_dir, output_file):
    """
    创建文件列表
    
    Args:
        h5_dir (str): H5文件目录路径
        output_file (str): 输出列表文件路径
    """
    print(f"🔍 扫描目录: {h5_dir}")
    
    # 检查目录是否存在
    if not os.path.exists(h5_dir):
        print(f"❌ 目录不存在: {h5_dir}")
        return
    
    # 获取所有.h5文件
    h5_files = []
    for filename in os.listdir(h5_dir):
        if filename.endswith('.h5'):
            full_path = os.path.abspath(os.path.join(h5_dir, filename))
            h5_files.append((filename, full_path))
    
    print(f"📊 找到 {len(h5_files)} 个H5文件")
    
    # 按文件名排序（数值排序）
    h5_files.sort(key=lambda x: get_sort_key(x[0]))
    
    # 创建输出目录
    output_dir = os.path.dirname(output_file)
    os.makedirs(output_dir, exist_ok=True)
    
    # 写入文件列表
    print(f"📝 写入文件列表: {output_file}")
    with open(output_file, 'w') as f:
        for filename, full_path in h5_files:
            f.write(full_path + '\n')
    
    print(f"✅ 完成！共写入 {len(h5_files)} 个文件路径")
    
    # 显示前10个和后10个文件作为验证
    print(f"\n📋 文件列表预览:")
    print(f"   前10个文件:")
    for i, (filename, _) in enumerate(h5_files[:10]):
        print(f"     {i+1:2d}. {filename}")
    
    if len(h5_files) > 20:
        print(f"   ... (省略 {len(h5_files) - 20} 个文件)")
        print(f"   后10个文件:")
        for i, (filename, _) in enumerate(h5_files[-10:], len(h5_files) - 9):
            print(f"     {i:2d}. {filename}")
    elif len(h5_files) > 10:
        print(f"   剩余文件:")
        for i, (filename, _) in enumerate(h5_files[10:], 11):
            print(f"     {i:2d}. {filename}")
    
    return len(h5_files)

def main():
    """主函数"""
    # 配置路径
    h5_dir = "/home/<USER>/data/tumor/Dataset017_Liver/h5"
    output_file = "/home/<USER>/data/tumor/split/tmp.list"
    
    print("📁 创建H5文件列表")
    print("=" * 60)
    print(f"📂 源目录: {h5_dir}")
    print(f"📄 输出文件: {output_file}")
    print("=" * 60)
    
    # 创建文件列表
    file_count = create_file_list(h5_dir, output_file)
    
    # 验证输出文件
    if os.path.exists(output_file):
        with open(output_file, 'r') as f:
            lines = f.readlines()
        
        print(f"\n🔍 验证输出文件:")
        print(f"   文件路径: {output_file}")
        print(f"   文件大小: {os.path.getsize(output_file)} bytes")
        print(f"   行数: {len(lines)}")
        
        # 显示前几行作为示例
        print(f"   前5行内容:")
        for i, line in enumerate(lines[:5]):
            print(f"     {i+1}. {line.strip()}")
        
        if len(lines) > 5:
            print(f"   ...")
            print(f"   最后一行: {lines[-1].strip()}")

if __name__ == "__main__":
    main()
