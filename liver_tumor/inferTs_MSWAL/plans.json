{"dataset_name": "Dataset003_Liver", "plans_name": "nnUNetPlans", "original_median_spacing_after_transp": [1.0, 0.767578125, 0.767578125], "original_median_shape_after_transp": [424, 512, 512], "image_reader_writer": "SimpleITKIO", "transpose_forward": [0, 1, 2], "transpose_backward": [0, 1, 2], "configurations": {"2d": {"data_identifier": "nnUNetPlans_2d", "preprocessor_name": "DefaultPreprocessor", "batch_size": 12, "patch_size": [512, 512], "median_image_size_in_voxels": [512.0, 512.0], "spacing": [0.767578125, 0.767578125], "normalization_schemes": ["CTNormalization"], "use_mask_for_norm": [false], "resampling_fn_data": "resample_data_or_seg_to_shape", "resampling_fn_seg": "resample_data_or_seg_to_shape", "resampling_fn_data_kwargs": {"is_seg": false, "order": 3, "order_z": 0, "force_separate_z": null}, "resampling_fn_seg_kwargs": {"is_seg": true, "order": 1, "order_z": 0, "force_separate_z": null}, "resampling_fn_probabilities": "resample_data_or_seg_to_shape", "resampling_fn_probabilities_kwargs": {"is_seg": false, "order": 1, "order_z": 0, "force_separate_z": null}, "architecture": {"network_class_name": "dynamic_network_architectures.architectures.unet.PlainConvUNet", "arch_kwargs": {"n_stages": 8, "features_per_stage": [32, 64, 128, 256, 512, 512, 512, 512], "conv_op": "torch.nn.modules.conv.Conv2d", "kernel_sizes": [[3, 3], [3, 3], [3, 3], [3, 3], [3, 3], [3, 3], [3, 3], [3, 3]], "strides": [[1, 1], [2, 2], [2, 2], [2, 2], [2, 2], [2, 2], [2, 2], [2, 2]], "n_conv_per_stage": [2, 2, 2, 2, 2, 2, 2, 2], "n_conv_per_stage_decoder": [2, 2, 2, 2, 2, 2, 2], "conv_bias": true, "norm_op": "torch.nn.modules.instancenorm.InstanceNorm2d", "norm_op_kwargs": {"eps": 1e-05, "affine": true}, "dropout_op": null, "dropout_op_kwargs": null, "nonlin": "torch.nn.LeakyReLU", "nonlin_kwargs": {"inplace": true}}, "_kw_requires_import": ["conv_op", "norm_op", "dropout_op", "nonlin"]}, "batch_dice": true}, "3d_lowres": {"data_identifier": "nnUNetPlans_3d_lowres", "preprocessor_name": "DefaultPreprocessor", "batch_size": 2, "patch_size": [128, 128, 128], "median_image_size_in_voxels": [192, 205, 205], "spacing": [2.5000803453253524, 1.9190069838141859, 1.9190069838141859], "normalization_schemes": ["CTNormalization"], "use_mask_for_norm": [false], "resampling_fn_data": "resample_data_or_seg_to_shape", "resampling_fn_seg": "resample_data_or_seg_to_shape", "resampling_fn_data_kwargs": {"is_seg": false, "order": 3, "order_z": 0, "force_separate_z": null}, "resampling_fn_seg_kwargs": {"is_seg": true, "order": 1, "order_z": 0, "force_separate_z": null}, "resampling_fn_probabilities": "resample_data_or_seg_to_shape", "resampling_fn_probabilities_kwargs": {"is_seg": false, "order": 1, "order_z": 0, "force_separate_z": null}, "architecture": {"network_class_name": "dynamic_network_architectures.architectures.unet.PlainConvUNet", "arch_kwargs": {"n_stages": 6, "features_per_stage": [32, 64, 128, 256, 320, 320], "conv_op": "torch.nn.modules.conv.Conv3d", "kernel_sizes": [[3, 3, 3], [3, 3, 3], [3, 3, 3], [3, 3, 3], [3, 3, 3], [3, 3, 3]], "strides": [[1, 1, 1], [2, 2, 2], [2, 2, 2], [2, 2, 2], [2, 2, 2], [2, 2, 2]], "n_conv_per_stage": [2, 2, 2, 2, 2, 2], "n_conv_per_stage_decoder": [2, 2, 2, 2, 2], "conv_bias": true, "norm_op": "torch.nn.modules.instancenorm.InstanceNorm3d", "norm_op_kwargs": {"eps": 1e-05, "affine": true}, "dropout_op": null, "dropout_op_kwargs": null, "nonlin": "torch.nn.LeakyReLU", "nonlin_kwargs": {"inplace": true}}, "_kw_requires_import": ["conv_op", "norm_op", "dropout_op", "nonlin"]}, "batch_dice": false, "next_stage": "3d_cascade_fullres"}, "3d_fullres": {"data_identifier": "nnUNetPlans_3d_fullres", "preprocessor_name": "DefaultPreprocessor", "batch_size": 2, "patch_size": [128, 128, 128], "median_image_size_in_voxels": [480.0, 512.0, 512.0], "spacing": [1.0, 0.767578125, 0.767578125], "normalization_schemes": ["CTNormalization"], "use_mask_for_norm": [false], "resampling_fn_data": "resample_data_or_seg_to_shape", "resampling_fn_seg": "resample_data_or_seg_to_shape", "resampling_fn_data_kwargs": {"is_seg": false, "order": 3, "order_z": 0, "force_separate_z": null}, "resampling_fn_seg_kwargs": {"is_seg": true, "order": 1, "order_z": 0, "force_separate_z": null}, "resampling_fn_probabilities": "resample_data_or_seg_to_shape", "resampling_fn_probabilities_kwargs": {"is_seg": false, "order": 1, "order_z": 0, "force_separate_z": null}, "architecture": {"network_class_name": "dynamic_network_architectures.architectures.unet.PlainConvUNet", "arch_kwargs": {"n_stages": 6, "features_per_stage": [32, 64, 128, 256, 320, 320], "conv_op": "torch.nn.modules.conv.Conv3d", "kernel_sizes": [[3, 3, 3], [3, 3, 3], [3, 3, 3], [3, 3, 3], [3, 3, 3], [3, 3, 3]], "strides": [[1, 1, 1], [2, 2, 2], [2, 2, 2], [2, 2, 2], [2, 2, 2], [2, 2, 2]], "n_conv_per_stage": [2, 2, 2, 2, 2, 2], "n_conv_per_stage_decoder": [2, 2, 2, 2, 2], "conv_bias": true, "norm_op": "torch.nn.modules.instancenorm.InstanceNorm3d", "norm_op_kwargs": {"eps": 1e-05, "affine": true}, "dropout_op": null, "dropout_op_kwargs": null, "nonlin": "torch.nn.LeakyReLU", "nonlin_kwargs": {"inplace": true}}, "_kw_requires_import": ["conv_op", "norm_op", "dropout_op", "nonlin"]}, "batch_dice": true}, "3d_cascade_fullres": {"inherits_from": "3d_fullres", "previous_stage": "3d_lowres"}}, "experiment_planner_used": "Experiment<PERSON><PERSON>ner", "label_manager": "LabelManager", "foreground_intensity_properties_per_channel": {"0": {"max": 5420.0, "mean": 99.75579601967164, "median": 101.0, "min": -983.0, "percentile_00_5": -15.0, "percentile_99_5": 198.0, "std": 37.135875761732805}}}