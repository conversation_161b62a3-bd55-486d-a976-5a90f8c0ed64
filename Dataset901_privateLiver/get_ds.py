import json
import os

all_ds = []

c = 0
for case in os.listdir("./labelsTr"):
        all_ds.append({
            "image": os.path.join("imagesTr", case),
            "label": os.path.join("labelsTr", case)
        })

training = all_ds[:2000]
val = all_ds[2000:]

ds = { 
    "name": "Liver", 
    "description": "Liver, and cancer segmentation",
    "reference": "xxxx",
    "licence":"CC-BY-SA 4.0",
    "release":"1.0 04/05/2025",
    "tensorImageSize": "3D",
    "file_ending": ".nii.gz",
    "channel_names": {
            "0": "nonCT"
        },
    "labels": { 
         "background": 0, 
         "liver": 1,
         "cancer": 2
     }, 
    "numTraining": len(all_ds), 
    "numTest": 0,
    "training": all_ds
}


with open("dataset.json", "w") as f:
    json.dump(ds, f, ensure_ascii=False, indent=4)